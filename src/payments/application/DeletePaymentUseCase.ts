import {
  contextualizeError,
  Either2,
  UniqueEntityID,
  type UseCase,
} from '@discocil/fv-domain-library';

import { PaymentRepository } from '../domain/contracts/PaymentRepository';
import { DeletePaymentEither } from '../domain/entities/Payment';
import { PaymentCriteriaMother } from '../domain/filters/PaymentCriteriaMother';

import type { DeletePaymentDto } from '../domain/contracts/DeletePaymentDtoContract';

export class DeletePaymentUseCase implements UseCase<DeletePaymentDto, Promise<DeletePaymentEither>> {
  constructor(private readonly paymentRepository: PaymentRepository) { }

  @contextualizeError()
  async execute(dto: DeletePaymentDto): Promise<DeletePaymentEither> {
    const paymentId = UniqueEntityID.build(dto.paymentId);

    const deleteCriteria = PaymentCriteriaMother.idToMatch(paymentId);

    const deletedCount = await this.paymentRepository.delete(deleteCriteria);

    return Either2.right(deletedCount);
  }
}
