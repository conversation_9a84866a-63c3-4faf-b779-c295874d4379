import type { Criteria } from '@discocil/fv-criteria-converter-library/domain';
import type { FvNumber } from '@discocil/fv-domain-library/domain';
import type {
  Payment,
  PaymentsEither,
} from '../entities/Payment';

export interface PaymentRepository {
  save: (payment: Payment) => Promise<void>;
  search: (criteria: Criteria) => Promise<PaymentsEither>;
  delete: (criteria: Criteria) => Promise<FvNumber>;
}
