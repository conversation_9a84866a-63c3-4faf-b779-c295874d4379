import {
  AggregateRoot,
  EResourceTypes,
  FvDate,
  Maybe,
  Money,
  UniqueEntityID,
  left,
  right,
} from '@discocil/fv-domain-library/domain';


import { defaultStamps, stampValueObjects } from '@/cross-cutting/domain/services/Stamps';


import { PaymentCreatedDomainEvent } from '../events/PaymentCreatedDomainEvent';
import { ResourceInfoToDomainFactory } from '../factories/ResourceInfoToDomainFactory';
import { ResourceInfoToPrimitiveFactory } from '../factories/ResourceInfoToPrimitiveFactory';
import { PaymentJsonMapper } from '../mappers/PaymentJsonMapper';

import type { CreateEntityPrimitives } from '@/cross-cutting/domain/contracts/CreateEntityPrimitives';
import type { MapperError } from '@/cross-cutting/domain/errors/MapperError';
import type { PaymentGatewayBadConfigurationError } from '@/gatewayAccount/domain/errors/PaymentGatewayBadConfigurationError';
import type { TemplatePrimitives } from '@/organizations/organizations/domain/entities/RuleTemplate';
import type { TDiscountCodeExchanged, TDiscountCodeExchangedPrimitive } from '@/tickets/discountCodes/domain/entities/DiscountCode';
import type { Sitting } from '@/tickets/tickets/domain/contracts/SittingContracts';
import type { Tickets } from '@/tickets/tickets/domain/entities/TicketEntity';
import type { PaginationMetadataResponse } from '@discocil/fv-criteria-converter-library/domain';
import type {
  CreatedAt,
  CreatedBy,
  DatePrimitive,
  ECurrency,
  EPaymentStates,
  EPaymentsProvider,
  ESaleTypes,
  Either,
  EitherType,
  FvNumber,
  IdPrimitive,
  InvalidArgumentError,
  MoneyEither,
  MoneyError,
  NotFoundError,
  Primitives,
  Properties,
  RemovedAt,
  RemovedBy,
  UnexpectedError,
  UpdatedAt,
  UpdatedBy,
} from '@discocil/fv-domain-library/domain';
import type { PaymentResourceInfo, PaymentResourceInfoPrimitives } from '../contracts/PaymentResources';

export type PaymentSupplements = {
  price: Money;
  number: number;
};

export type PaymentWarranty = {
  total: Money;
  cost: Money;
};

type Seat = Sitting & { ticketId: string; };

export type PaymentSitting = {
  holdToken: string | null;
  seats: Seat[];
};

export type PaymentSpotify = {
  pzClickref: string;
};

export type PaymentCustomerCustomFees = {
  taxes: Money;
  fees: Money;
};

export type PaymentCustomerCustomFeesPrimitives = Primitives<PaymentCustomerCustomFees>;

export type Payments = Map<IdPrimitive, Payment>;

type PaymentWithTickets = {
  payment: Payment;
  tickets: Tickets;
};
export type PaymentsWithTickets = Map<IdPrimitive, PaymentWithTickets>;

export type SearchPaginatedPaymentsResponse = PaginationMetadataResponse & {
  readonly paymentsWithTickets: PaymentsWithTickets;
  readonly tickets: Tickets;
};

export type PaymentsPaginated = PaginationMetadataResponse & {
  readonly payments: Payments;
};

export type DeletePaymentEither = EitherType<UnexpectedError, FvNumber>;

export type PaymentEither = Either<MapperError | NotFoundError | PaymentGatewayBadConfigurationError | InvalidArgumentError | MoneyError, Payment>;
export type PaymentsEither = EitherType<
  MapperError
  | NotFoundError
  | PaymentGatewayBadConfigurationError
  | InvalidArgumentError
  | MoneyError,
  PaymentsPaginated
>;

export type SearchPaginatedPaymentsEither = EitherType<
  MapperError
  | NotFoundError
  | PaymentGatewayBadConfigurationError
  | InvalidArgumentError
  | MoneyError,
  SearchPaginatedPaymentsResponse
>;

export type PaymentSupplementsPrimitives = Primitives<PaymentSupplements>;

export type PaymentWarrantyPrimitives = Primitives<PaymentWarranty>;

export type PaymentPrimitives = Properties<Primitives<Omit<Payment, 'numberSupplements' | 'priceSupplements' | 'totalWarranty' | 'costWarranty' | 'info' | 'customerCustomFees'>> & {
  supplements: PaymentSupplementsPrimitives;
  warranty: PaymentWarrantyPrimitives;
  info: PaymentResourceInfoPrimitives;
  customerCustomFees: Maybe<PaymentCustomerCustomFeesPrimitives>;
}>;

export type CreatePaymentPrimitives = CreateEntityPrimitives<PaymentPrimitives>;

export type PaymentKeys = keyof Properties<Payment>;

export class Payment extends AggregateRoot {
  private constructor(
    id: UniqueEntityID,
    private readonly _organizationId: UniqueEntityID,
    private readonly _eventId: Maybe<UniqueEntityID>,
    private readonly _typeId: Maybe<UniqueEntityID>,
    private readonly _userId: Maybe<UniqueEntityID>,
    readonly resourceType: EResourceTypes,
    readonly totalImportCts: number,
    private readonly _resourceIds: UniqueEntityID[],
    private readonly _feesTotal: Money,
    private readonly _discocilTotal: Money,
    private readonly _totalPrice: Money,
    private readonly _totalImport: Money,
    private readonly _paid: Maybe<FvDate>,
    private readonly _info: PaymentResourceInfo,
    readonly nResources: number,
    private readonly _expiresAt: FvDate,
    readonly state: EPaymentStates,
    private _redirectUrl: Maybe<string>,
    readonly errorUrl: Maybe<string>,
    readonly paymentsProvider: EPaymentsProvider,
    readonly merchantName: Maybe<string>,
    private _paylinkId: Maybe<UniqueEntityID>,
    readonly totalDiscount: number,
    private readonly _supplements: PaymentSupplements,
    private readonly _warranty: PaymentWarranty,
    private readonly _discount: Maybe<TDiscountCodeExchanged>,
    readonly currency: ECurrency,
    private _sitting: Maybe<PaymentSitting>,
    readonly spotify: Maybe<PaymentSpotify>,
    readonly isFourvenuesGateway: boolean,
    private readonly _gatewayAccountId: Maybe<UniqueEntityID>,
    private readonly _createdAt: CreatedAt,
    private readonly _createdBy: CreatedBy,
    private readonly _updatedAt: UpdatedAt,
    private readonly _updatedBy: UpdatedBy,
    private readonly _removedAt: RemovedAt,
    private readonly _removedBy: RemovedBy,
    readonly commissionRule: Maybe<TemplatePrimitives>,
    readonly saleType: ESaleTypes,
    private readonly _customerCustomFees: Maybe<PaymentCustomerCustomFees>,
  ) {
    super(id);
  }

  static build(primitives: PaymentPrimitives): PaymentEither {
    const buildMoney = (amount: number, currency: ECurrency): MoneyEither => Money.build({ amount, currency });

    const buildDiscount = (discount: TDiscountCodeExchangedPrimitive): TDiscountCodeExchanged => {
      return {
        id: UniqueEntityID.build(discount.id),
        code: discount.code,
        exchangedId: discount.exchangedId.map(item => UniqueEntityID.build(item)),
      };
    };

    const id = UniqueEntityID.build(primitives.id);
    const organizationId = UniqueEntityID.build(primitives.organizationId);
    const resourceIds = primitives.resourceIds.map(id => UniqueEntityID.build(id));
    const expiresAt = FvDate.create(primitives.expiresAt);

    const eventId = primitives.eventId.map(item => UniqueEntityID.build(item));
    const typeId = primitives.typeId.map(item => UniqueEntityID.build(item));
    const userId = primitives.userId.map(item => UniqueEntityID.build(item));
    const paid = primitives.paid.map(item => FvDate.create(item));
    const paylinkId = primitives.paylinkId.map(item => UniqueEntityID.build(item));
    const discount = primitives.discount.map(item => buildDiscount(item));
    const gatewayAccountId = primitives.gatewayAccountId.map(item => UniqueEntityID.build(item));

    const redirectUrl = primitives.redirectUrl.map(item => item);
    const errorUrl = primitives.errorUrl.map(item => item);
    const sitting = primitives.sitting.map(item => item);
    const spotify = primitives.spotify.map(item => item);
    const merchantName = primitives.merchantName.map(item => item);
    const commissionRule = primitives.commissionRule.map(item => item);

    const stamps = stampValueObjects(primitives);

    // eslint-disable-next-line no-restricted-syntax
    const info = ResourceInfoToDomainFactory.build(primitives.resourceType, primitives.info, primitives.currency);

    const feesTotalResult = buildMoney(primitives.feesTotal, primitives.currency);
    const discocilTotalResult = buildMoney(primitives.discocilTotal, primitives.currency);
    const totalPriceResult = buildMoney(primitives.totalPrice, primitives.currency);
    const totalImportResult = buildMoney(primitives.totalImport, primitives.currency);
    const supplementsPriceResult = buildMoney(primitives.supplements.price, primitives.currency);
    const warrantyTotalResult = buildMoney(primitives.warranty.total, primitives.currency);
    const warrantyCostResult = buildMoney(primitives.warranty.cost, primitives.currency);

    if (feesTotalResult.isLeft()) {
      return left(feesTotalResult.value);
    }

    if (discocilTotalResult.isLeft()) {
      return left(discocilTotalResult.value);
    }

    if (totalPriceResult.isLeft()) {
      return left(totalPriceResult.value);
    }

    if (totalImportResult.isLeft()) {
      return left(totalImportResult.value);
    }

    if (supplementsPriceResult.isLeft()) {
      return left(supplementsPriceResult.value);
    }

    if (warrantyTotalResult.isLeft()) {
      return left(warrantyTotalResult.value);
    }

    if (warrantyCostResult.isLeft()) {
      return left(warrantyCostResult.value);
    }

    const feesTotal = feesTotalResult.value;
    const discocilTotal = discocilTotalResult.value;
    const totalPrice = totalPriceResult.value;
    const totalImport = totalImportResult.value;

    const supplements: PaymentSupplements = {
      ...primitives.supplements,
      price: supplementsPriceResult.value,
    };

    const warranty: PaymentWarranty = {
      total: warrantyTotalResult.value,
      cost: warrantyCostResult.value,
    };

    let customerCustomFees = Maybe.none<PaymentCustomerCustomFees>();

    if (primitives.customerCustomFees.isDefined()) {
      const taxes = buildMoney(primitives.customerCustomFees.get().taxes, primitives.currency);
      const fees = buildMoney(primitives.customerCustomFees.get().fees, primitives.currency);

      if (taxes.isLeft()) {
        return left(taxes.value);
      }

      if (fees.isLeft()) {
        return left(fees.value);
      }

      customerCustomFees = Maybe.some({
        taxes: taxes.value,
        fees: fees.value,
      });
    }

    const entity = new Payment(
      id,
      organizationId,
      eventId,
      typeId,
      userId,
      primitives.resourceType,
      primitives.totalImportCts,
      resourceIds,
      feesTotal,
      discocilTotal,
      totalPrice,
      totalImport,
      paid,
      info,
      primitives.nResources,
      expiresAt,
      primitives.state,
      redirectUrl,
      errorUrl,
      primitives.paymentsProvider,
      merchantName,
      paylinkId,
      primitives.totalDiscount,
      supplements,
      warranty,
      discount,
      primitives.currency,
      sitting,
      spotify,
      primitives.isFourvenuesGateway,
      gatewayAccountId,
      stamps.createdAt,
      stamps.createdBy,
      stamps.updatedAt,
      stamps.updatedBy,
      stamps.removedAt,
      stamps.removedBy,
      commissionRule,
      primitives.saleType,
      customerCustomFees,
    );

    return right(entity);
  }

  static create(primitives: CreatePaymentPrimitives): PaymentEither {
    const id = UniqueEntityID.create().toPrimitive();

    const entityResult = Payment.build({
      ...primitives, id, ...defaultStamps(),
    });

    if (entityResult.isLeft()) {
      return left(entityResult.value);
    }

    const entity = entityResult.value;

    entity.addDomainEvent(
      PaymentCreatedDomainEvent.build({
        aggregateId: entity._id,
        attributes: PaymentJsonMapper.toJson(entity),
      }),
    );

    return right(entity);
  }

  get organizationId(): IdPrimitive {
    return this._organizationId.toPrimitive();
  }

  get eventId(): Maybe<IdPrimitive> {
    return this._eventId.map(id => id.toPrimitive());
  }

  get typeId(): Maybe<IdPrimitive> {
    return this._typeId.map(id => id.toPrimitive());
  }

  get userId(): Maybe<IdPrimitive> {
    return this._userId.map(id => id.toPrimitive());
  }

  get resourceIds(): IdPrimitive[] {
    return this._resourceIds.map(recursoId => recursoId.toPrimitive());
  }

  get feesTotal(): number {
    return this._feesTotal.toDecimal();
  }

  get discocilTotal(): number {
    return this._discocilTotal.toDecimal();
  }

  get totalPrice(): number {
    return this._totalPrice.toDecimal();
  }

  get totalImport(): number {
    return this._totalImport.toDecimal();
  }

  get paid(): Maybe<DatePrimitive> {
    return this._paid.map(date => date.toPrimitive());
  }

  get expiresAt(): DatePrimitive {
    return this._expiresAt.toPrimitive();
  }

  get paylinkId(): Maybe<IdPrimitive> {
    return this._paylinkId.map(id => id.toPrimitive());
  }

  get gatewayAccountId(): Maybe<IdPrimitive> {
    return this._gatewayAccountId.map(id => id.toPrimitive());
  }

  get info(): PaymentResourceInfoPrimitives {
    return ResourceInfoToPrimitiveFactory.build(this.resourceType, this._info);
  }

  get totalWarranty(): number {
    return this._warranty.total.toDecimal();
  }

  get costWarranty(): number {
    return this._warranty.cost.toDecimal();
  }

  get numberSupplements(): number {
    return this._supplements.number;
  }

  get priceSupplements(): number {
    return this._supplements.price.toDecimal();
  }

  get discount(): Maybe<TDiscountCodeExchangedPrimitive> {
    if (this._discount.isEmpty()) {
      return Maybe.none();
    }

    const discount = this._discount.get();

    return Maybe.some({
      ...discount,
      id: discount.id.toPrimitive(),
      exchangedId: discount.exchangedId.map(item => item.toPrimitive()),
    });
  }

  get redirectUrl(): Maybe<string> {
    return this._redirectUrl;
  }

  get sitting(): Maybe<PaymentSitting> {
    return this._sitting;
  }

  get customerCustomFees(): Maybe<PaymentCustomerCustomFeesPrimitives> {
    if (this._customerCustomFees.isEmpty()) {
      return Maybe.none<PaymentCustomerCustomFeesPrimitives>();
    }

    const customerCustomFees = this._customerCustomFees.get();

    return Maybe.some({
      taxes: customerCustomFees.taxes.toDecimal(),
      fees: customerCustomFees.fees.toDecimal(),
    });
  }

  get createdAt(): number {
    return this._createdAt.toMilliseconds();
  }

  get createdBy(): string {
    return this._createdBy.toPrimitive();
  }

  get updatedAt(): number {
    return this._updatedAt.toMilliseconds();
  }

  get updatedBy(): string {
    return this._updatedBy.toPrimitive();
  }

  get removedAt(): number {
    return this._removedAt.toMilliseconds();
  }

  get removedBy(): string {
    return this._removedBy.toPrimitive();
  }

  setPaylinkId(paylinkId: string): this {
    this._paylinkId = Maybe.some(UniqueEntityID.build(paylinkId));

    return this;
  }

  setRedirectUrl(value: string): this {
    this._redirectUrl = Maybe.some(value);

    return this;
  }

  isRequired(): boolean {
    return this._paid.isEmpty() && this._totalImport.isPositive();
  }

  hasNeedSale(): boolean {
    return this._totalImport.isZero();
  }

  setSitting(value: PaymentSitting): this {
    this._sitting = Maybe.some(value);

    return this;
  }

  getPaidInISO(): Maybe<string> {
    return this._paid.map(date => date.toISO());
  }

  getExpiresAtInISO(): string {
    return this._expiresAt.toISO();
  }

  getExpiresAtInMilliseconds(): number {
    return this._expiresAt.toMilliseconds();
  }

  isForTicket(): boolean {
    return this.eventId.isDefined() && this.resourceType === EResourceTypes.TICKETS;
  }

  includesResourceId(resourceId: IdPrimitive): boolean {
    return this._resourceIds.some(id => id.equals(resourceId));
  }
}
